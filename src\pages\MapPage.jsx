import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSON, Popup } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import { stateService } from '../services/api/state.service';
import Navbar from '../components/Navbar';
import { generateStateColor } from '../utils/colorGenerator';
import { Link } from 'react-router-dom';
import { useAuthGuard } from '../hooks/useAuthGuard';

// Store state colors
const stateColorMap = new Map();

export default function MapPage() {
  useAuthGuard();
  const [states, setStates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [geoJsonData, setGeoJsonData] = useState(null);


useEffect(() => {
  const fetchGeoJson = async () => {
    try {
      const response = await fetch('https://raw.githubusercontent.com/datasets/geo-countries/master/data/countries.geojson');
      if (!response.ok) throw new Error('Failed to fetch GeoJSON');
      const data = await response.json();
      setGeoJsonData(data);
    } catch (error) {
      console.error('Error fetching GeoJSON:', error);
    }
  };

  fetchGeoJson();
}, []);



  useEffect(() => {
    const fetchStates = async () => {
      try {
        const states = await stateService.getAllStates();
        setStates(states);
      } catch (err) {
        setError('Failed to fetch map data');
        console.error('Error fetching map data:', err);
      } finally {
        setLoading(false);
      }
    };
  
    fetchStates();
  }, []);
  

  const getStateColor = (stateId) => {
    if (!stateColorMap.has(stateId)) {
      stateColorMap.set(stateId, generateStateColor(stateColorMap.size));
    }
    return stateColorMap.get(stateId);
  };

  const findStateAndRegionByCountryName = (countryName) => {
    for (const state of states) {
      const region = state.regions.find(r => r.name === countryName);
      if (region) {
        return { state, region };
      }
    }
    return null;
  };

  const getGeoJSONStyle = (feature) => {
    // Match using ISO3166-1-Alpha-3 code (3-letter country code)
    const countryName = feature.properties['name'];
    const stateAndRegion = findStateAndRegionByCountryName(countryName);

    return {
      fillColor: stateAndRegion ? getStateColor(stateAndRegion.state.id) : '#444444',
      weight: 1,
      opacity: 1,
      color: 'white',
      dashArray: '3',
      fillOpacity: stateAndRegion ? 0.6 : 0.2
    };
  };

  const onEachFeature = (feature, layer) => {
    const countryName = feature.properties['name'];
    const stateAndRegion = findStateAndRegionByCountryName(countryName);

    if (stateAndRegion) {
      const { state, region } = stateAndRegion;
      layer.bindPopup(`
        <div class="p-2">
          <h3 class="font-bold text-lg text-gray-900">${region.name}</h3>
          <div class="w-full h-1 my-2" style="background-color: ${getStateColor(state.id)}"></div>
          <p class="text-gray-700">State: ${state.name}</p>
          <p class="text-gray-700">Population: ${region.population?.toLocaleString() || 'Unknown'}</p>
          <div class="mt-2">
            <a href="/states/${state.id}" class="text-blue-600 hover:text-blue-800">
              View Details →
            </a>
          </div>
        </div>
      `);

      // Highlight on hover
      layer.on({
        mouseover: (e) => {
          const layer = e.target;
          layer.setStyle({
            weight: 2,
            fillOpacity: 0.8,
          });
        },
        mouseout: (e) => {
          const layer = e.target;
          layer.setStyle({
            weight: 1,
            fillOpacity: 0.6,
          });
        }
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-neonBlue text-xl">Loading map data...</div>
        </div>
      </div>
    );
  }
  
  if (error) return <div className="min-h-screen bg-gray-900"><Navbar /><div className="flex items-center justify-center h-[calc(100vh-4rem)]"><div className="text-red-500 text-xl">{error}</div></div></div>;

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="h-[calc(100vh-4rem)] relative">
        <MapContainer
          center={[41.5, 20]} // Centered on Balkans
          zoom={2}
          style={{ height: '100%', width: '100%' }}
          minZoom={3}
          maxBounds={[[-90, -180], [90, 180]]}
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />
        {geoJsonData && (
          <GeoJSON
            data={geoJsonData}
            style={getGeoJSONStyle}
            onEachFeature={onEachFeature}
          />
        )}

        </MapContainer>

        {/* Legend */}
        {/* <div className="absolute bottom-4 right-4 bg-gray-800/90 backdrop-blur-sm rounded-lg shadow-lg p-4">
          <h3 className="text-white font-bold mb-2">States</h3>
          <div className="space-y-2">
            {states.map(state => (
              <div key={state.id} className="flex items-center gap-2">
                <div 
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: getStateColor(state.id) }}
                />
                <span className="text-white text-sm">
                  {state.name} ({state.regions.length} regions)
                </span>
              </div>
            ))}
          </div>
        </div> */}
      </div>
    </div>
  );
}






