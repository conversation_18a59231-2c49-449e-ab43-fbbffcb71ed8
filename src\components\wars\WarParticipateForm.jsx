import React, { useState, useEffect } from "react";
import { warService } from "../../services/api/war.service";

import useUserDataStore from "../../store/useUserDataStore";
import { showSuccessToast } from "../../utils/showSuccessToast";
import { showErrorToast } from "../../utils/showErrorToast";

const WarParticipateForm = ({ warId, onParticipationComplete }) => {
  // We need userData for premium status check and energy amount
  const { userData, fetchUserData } = useUserDataStore();
  const [loading, setLoading] = useState(false);
  const [autoMode, setAutoMode] = useState(false);

  // Check if user has premium
  const isPremium = userData?.isPremium || false;

  // Get current user energy
  const currentEnergy = userData?.energy || 0;

  const handleAutoModeChange = (e) => {
    setAutoMode(e.target.checked);
  };

  useEffect(() => {
    if(!userData) return;

    if(userData.activeAutoMode === "war") {
      setAutoMode(true);
    }
    console.log(autoMode,'autoMode')
  }, []);

  const handleManualAttack = async () => {
    setLoading(true);

    try {
      // Refresh user data to get the latest energy amount
      await fetchUserData(true);

      // Get the latest energy value
      const latestEnergy = userData?.energy || 0;

      if (latestEnergy <= 0) {
        showErrorToast(
          "You do not have enough energy to participate in the war."
        );
        return;
      }

      // Manual attack uses all energy
      const result = await warService.participateInWar(warId, {
        energyAmount: latestEnergy, // Use 100% of energy
        autoMode: false,
      });

      showSuccessToast("Successfully participated in war!");

      // Refresh user data to update energy display
      await fetchUserData(true);

      // Notify parent component
      if (onParticipationComplete) {
        onParticipationComplete(result);
      }
    } catch (error) {
      console.error("Failed to participate in war:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  const handleAutoModeSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Refresh user data to get the latest energy amount
      await fetchUserData(true);

      // Get the latest energy value
      const latestEnergy = userData?.energy || 0;

      if (latestEnergy <= 0) {
        showErrorToast(
          "You do not have enough energy to participate in the war."
        );
        return;
      }

      // Enable auto mode
      const result = await warService.participateInWar(warId, {
        energyAmount: latestEnergy, // Not used for auto mode
        autoMode: true,
        autoEnergyPercentage: 100, // Always use 100% in auto mode
      });

      showSuccessToast(
        "Auto mode enabled! The system will attack every 30 minutes."
      );

      // Refresh user data to update energy display
      await fetchUserData(true);

      // Notify parent component
      if (onParticipationComplete) {
        onParticipationComplete(result);
      }
    } catch (error) {
      console.error("Failed to enable auto mode:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6 mt-6">
      <h2 className="text-xl font-bold text-white mb-4">Participate in War</h2>

      {/* Energy Display */}
      <div className="mb-4 p-3 bg-gray-700 rounded-md">
        <div className="flex justify-between items-center">
          <span className="text-white font-medium">Your Energy:</span>
          <span
            className={`font-bold ${
              currentEnergy > 0 ? "text-neonBlue" : "text-red-400"
            }`}
          >
            {currentEnergy}
          </span>
        </div>
      </div>

      <div className="space-y-6">
        {/* Manual Attack Button */}
        <div>
          <h3 className="text-lg font-medium text-white mb-2">Manual Attack</h3>
          <p className="text-gray-300 mb-4">
            Attack now using all your available energy.
          </p>
          <button
            onClick={handleManualAttack}
            disabled={loading || currentEnergy <= 0}
            className="w-full bg-red-600 hover:bg-red-700 font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            {loading
              ? "Processing..."
              : currentEnergy <= 0
              ? "No Energy"
              : "Attack Now"}
          </button>
        </div>

        {/* Auto Mode Section */}
        <div className="border-t border-gray-700 pt-6">
          <h3 className="text-lg font-medium text-white mb-2">
            Auto Mode {!isPremium && "(Premium Only)"}
          </h3>

          <div className="bg-gray-700 p-4 rounded-md text-sm text-gray-300 mb-4">
            <p className="mb-2">
              When auto mode is enabled, the system will automatically attack
              every 30 minutes using all your available energy.
            </p>
            {!isPremium && (
              <p className="text-yellow-400 mt-2">
                This feature requires a premium account. Upgrade to premium to
                enable auto mode.
              </p>
            )}
          </div>

          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="autoMode"
              checked={autoMode}
              onChange={handleAutoModeChange}
              disabled={!isPremium || loading}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 rounded disabled:opacity-50"
            />
            <label
              htmlFor="autoMode"
              className={`ml-2 block text-sm ${
                !isPremium ? "text-gray-500" : "text-gray-300"
              }`}
            >
              Enable Auto Mode
            </label>
          </div>

          {autoMode && isPremium && (
            <button
              onClick={handleAutoModeSubmit}
              disabled={loading || currentEnergy <= 0}
              className="w-full bg-blue-600 hover:bg-blue-700 font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading
                ? "Processing..."
                : currentEnergy <= 0
                ? "No Energy"
                : "Enable Auto Mode"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default WarParticipateForm;
